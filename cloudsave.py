import requests
import json
import re
import time
import logging
import urllib.parse
from datetime import datetime
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Tuple, Optional, Dict, List
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import urllib3
from collections import defaultdict

# 禁用未验证HTTPS请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 夸克网盘请求头
quark_headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'cache-control': 'no-cache',
    'dnt': '1',
    'origin': 'https://pan.quark.cn',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://pan.quark.cn/',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
}

# 配置日志 - 设置为CRITICAL级别以屏蔽所有调试信息
logging.basicConfig(
    level=logging.CRITICAL,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def extract_episode_number(title):
    """
    从影视剧标题中提取集数

    Args:
        title (str): 影视剧标题

    Returns:
        int: 提取到的集数，如果没有找到返回None
    """

    # 定义多种集数匹配模式，按优先级排序
    patterns = [
        # 模式1: 【更新至X集】【更新至第X集】
        r'【更新至(?:第\s*)?(\d+)\s*集】',

        # 模式2: 【更新X集】【更X集】
        r'【更(?:新\s*)?(\d+)\s*集】',

        # 模式3: 【更新 X 集】（带空格）
        r'【更新\s+(\d+)\s+集】',

        # 模式4: [更至X集] [更新至X集] [更新X集]
        r'\[更(?:新)?(?:至)?(?:第\s*)?(\d+)\s*集\]',

        # 模式5: 《刷新至第X集》
        r'《刷新至第(\d+)集》',

        # 模式6: 【更新至第 X 集】（带空格和"第"）
        r'【更新至第\s*(\d+)\s*集】',

        # 模式7: 【更新至X集/单集XG】（提取第一个数字）
        r'【更新至(\d+)集[^】]*】',

        # 模式8: 【更新到第X集】
        r'【更新到第(\d+)集】',

        # 模式9: 【持续更新至X集】
        r'【持续更新至(\d+)集】',

        # 模式10: 更新至X集（无括号）
        r'更新至(\d+)集',

        # 模式11: 【更X/Y集】格式（取第一个数字）
        r'【更(\d+)/\d+集】',

        # 模式11.1: 【更 X/Y 集】格式（带空格，取第一个数字）
        r'【更\s+(\d+)/\d+\s+集】',

        # 模式12: 映至X集（繁体）
        r'映至(\d+)集',

        # 模式13: 已更至最新X集
        r'已更至最新(\d+)集',

        # 模式14: 更至第X集
        r'更至第(\d+)集',

        # 模式15: [更新至X集]
        r'\[更新至(\d+)集\]',

        # ===== 新增：无"集"字的模式 =====
        # 模式16: 【更至X】（无"集"字）
        r'【更至(\d+)】',

        # 模式17: [更至X]（无"集"字）
        r'\[更至(\d+)\]',

        # 模式18: 更新X（无"集"字，直接跟数字，排除视频格式）
        r'更新(\d+)(?![\d\.]|[kKpP])',

        # 模式19: 【更X】（无"新"和"集"，排除视频格式）
        r'【更(\d+)】(?![kKpP])',

        # 模式20: 更至X（无括号无"集"，排除视频格式）
        r'更至(\d+)(?![\d\.]|[kKpP])',

        # 模式21: 【更新X】（无"集"字，排除视频格式）
        r'【更新(\d+)】(?![kKpP])',

        # ===== 新增：特殊格式模式 =====
        # 模式22: 更EX 或 更E X（E表示Episode）
        r'更E\s*(\d+)',

        # 模式23: .更X（点号后面跟更X）
        r'\.更(\d+)',

        # 模式24: 更X（直接跟数字，无其他字符，排除视频格式）
        r'(?<![更新至到])更(\d+)(?![集\dkKpP])',

        # ===== 原有模式 =====
        # 模式25: 通用模式 - 更新相关的数字+集
        r'(?:更新?|刷新)(?:至)?(?:第\s*)?(\d+)\s*集',

        # 模式26: 兜底模式 - 任何地方的"X集"（排除视频格式）
        r'(?:第\s*)?(\d+)\s*集(?![kKpP])',
    ]

    # 按优先级尝试每个模式
    for pattern in patterns:
        matches = re.findall(pattern, title, re.IGNORECASE)
        if matches:
            # 如果有多个匹配，取最大的数字（通常是最新集数）
            episode_num = max(int(match) for match in matches)
            return episode_num

    return None

def get_quark_video_count(link: str, max_retries: int = 3, retry_delay: int = 2) -> int:
    """
    获取夸克分享链接的视频文件数量

    Args:
        link: 夸克分享链接
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）

    Returns:
        int: 视频文件数量，如果获取失败返回0
    """
    session = None
    try:
        session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=5,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["GET", "POST"]
        )

        # 为http和https配置重试适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        for attempt in range(max_retries):
            try:
                # 提取短链接和pwd_id
                short_url = link.split('#')[0]
                pwd_id = short_url.split('/s/')[1]

                params = {
                    'pr': 'ucpro',
                    'fr': 'pc',
                    'uc_param_str': '',
                    '__dt': '1003',
                    '__t': str(int(time.time() * 1000)),
                }

                json_data = {
                    'pwd_id': pwd_id,
                    'passcode': '',
                }

                # 获取token
                token_url = 'https://drive-h.quark.cn/1/clouddrive/share/sharepage/token'
                response = session.post(
                    token_url,
                    params=params,
                    headers=quark_headers,
                    json=json_data,
                    timeout=10,
                    verify=False
                )

                response_data = response.json()
                if response.status_code == 404 or response_data.get("status") == 404:
                    return 0

                # 验证响应数据
                if not isinstance(response_data, dict):
                    raise ValueError("响应数据格式错误")

                if "data" not in response_data or "stoken" not in response_data.get("data", {}):
                    raise ValueError("响应数据缺少必要字段")

                stoken = response_data["data"]["stoken"]
                encoded_stoken = urllib.parse.quote(stoken, safe='')

                # 获取详情
                detail_url = f'https://drive-h.quark.cn/1/clouddrive/share/sharepage/detail?pr=ucpro&fr=pc&uc_param_str=&pwd_id={pwd_id}&stoken={encoded_stoken}&pdir_fid=0&force=0&_page=1&_size=200&_fetch_banner=1&_fetch_share=1&_fetch_total=1&_sort=file_type:asc,updated_at:desc&__dt=633&__t={str(int(time.time() * 1000))}'

                # 添加详情请求的重试逻辑
                for detail_attempt in range(max_retries):
                    try:
                        response = session.get(
                            url=detail_url,
                            headers=quark_headers,
                            timeout=10,
                            verify=False
                        )
                        data = response.json()

                        if "data" not in data or "list" not in data["data"] or not data["data"]["list"]:
                            if detail_attempt < max_retries - 1:
                                time.sleep(retry_delay)
                                continue
                            return 0

                        # 如果成功获取数据，跳出重试循环
                        break

                    except (requests.RequestException, ValueError, json.JSONDecodeError) as e:
                        if detail_attempt < max_retries - 1:
                            time.sleep(retry_delay)
                            continue
                        return 0

                # 获取文件列表
                file_list = data["data"]["list"]

                # 如果只有一个文件夹，获取该文件夹内的文件列表
                if len(file_list) == 1 and file_list[0]["dir"]:
                    # 获取文件夹内容
                    folder_fid = file_list[0]["fid"]
                    folder_detail_url = f'https://drive-h.quark.cn/1/clouddrive/share/sharepage/detail?pr=ucpro&fr=pc&uc_param_str=&pwd_id={pwd_id}&stoken={encoded_stoken}&pdir_fid={folder_fid}&force=0&_page=1&_size=200&_fetch_banner=1&_fetch_share=1&_fetch_total=1&_sort=file_type:asc,updated_at:desc&__dt=633&__t={str(int(time.time() * 1000))}'

                    response = session.get(
                        url=folder_detail_url,
                        headers=quark_headers,
                        timeout=10,
                        verify=False
                    )
                    folder_data = response.json()

                    if "data" in folder_data and "list" in folder_data["data"]:
                        file_list = folder_data["data"]["list"]

                # 统计视频文件数量
                video_count = 0

                def is_video_file(item):
                    """判断是否为视频文件"""
                    # 如果是文件夹，直接返回False
                    if item.get("dir", False):
                        return False

                    # 方法1: 检查format_type是否以video/开头
                    format_type = item.get("format_type", "")
                    if format_type.startswith("video/"):
                        return True

                    # 方法2: 检查obj_category是否为video
                    obj_category = item.get("obj_category", "")
                    if obj_category == "video":
                        return True

                    # 方法3: 检查文件扩展名
                    file_name = item.get("file_name", "")
                    if "." in file_name:
                        file_ext = file_name.split(".")[-1].lower()
                        video_extensions = [
                            'mp4', 'mkv', 'avi', 'mov', 'wmv', 'flv', 'm4v',
                            'webm', 'ts', 'm2ts', 'rmvb', 'rm', '3gp', 'asf',
                            'mpg', 'mpeg', 'vob', 'f4v', 'swf'
                        ]
                        if file_ext in video_extensions:
                            return True

                    return False

                for item in file_list:
                    if is_video_file(item):
                        video_count += 1

                return video_count

            except (requests.RequestException, ValueError, KeyError) as e:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                return 0

    except Exception as e:
        return 0
    finally:
        if session:
            session.close()

def process_quark_link(item_data):
    """处理单个夸克链接的视频文件数量"""
    try:
        link = item_data['link']
        title = item_data['title'][:60]
        print(f"🔍 正在检测: {title}...")

        video_count = get_quark_video_count(link)
        item_data['video_count'] = video_count

        if video_count > 0:
            print(f"✅ 检测成功: {title} -> 视频文件数: {video_count}")
        else:
            print(f"❌ 链接失效或无视频: {title}")

        return item_data
    except Exception as e:
        print(f"❌ 夸克链接处理失败: {item_data['title'][:50]}... -> {str(e)}")
        item_data['video_count'] = 0
        return item_data

def get_authorization_token():
    """获取登录token"""
    login_url = 'https://cs.612625.xyz:9999/api/user/login'
    login_headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://cs.612625.xyz:9999',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
    }

    login_data = {
        "username": "admin",
        "password": "ChB154Cz2D^I8l"
    }

    try:
        response = requests.post(login_url, headers=login_headers, json=login_data)
        result = response.json()

        if result.get('success') and result.get('data', {}).get('token'):
            token = result['data']['token']
            return f"Bearer {token}"
        else:
            print(f"❌ 登录失败: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取token时出错: {e}")
        return None

# 获取authorization token
auth_token = get_authorization_token()

if not auth_token:
    print("❌ 无法获取authorization token，程序退出")
    exit(1)

headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'authorization': auth_token,
    'cache-control': 'no-cache',
    'dnt': '1',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
}

search_keyword = '正当防卫'
params = (
    ('keyword', search_keyword),
    ('lastMessageId', ''),
)

# 记录开始时间
start_time = time.time()
print(f"🚀 开始搜索:{search_keyword} {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

response = requests.get('https://cs.612625.xyz:9999/api/search', headers=headers, params=params)
data = response.json()

# 记录API请求完成时间
api_time = time.time()
print(f"📡 API请求完成，耗时: {api_time - start_time:.2f}秒")

# 添加调试信息

if data.get('data'):
    total_channels = len(data['data'])
    total_items = sum(len(channel.get('list', [])) for channel in data['data'])
else:
    print("❌ API未返回数据或数据格式异常")

# 根据链接URL判断云盘类型的函数
def get_cloud_type_from_url(url):
    import re
    from urllib.parse import urlparse

    # 夸克网盘
    if 'quark.cn' in url or 'pan.quark.cn' in url:
        return 'quark'
    # 百度网盘
    elif 'baidu.com' in url or 'pan.baidu.com' in url:
        return 'baidu'
    # 阿里云盘
    elif 'alipan.com' in url or 'aliyun' in url:
        return 'aliyun'
    # 天翼云盘
    elif '189.cn' in url or 'cloud.189.cn' in url:
        return 'tianyi'
    # 迅雷网盘
    elif 'xunlei.com' in url or 'pan.xunlei.com' in url:
        return 'xunlei'
    # UC网盘
    elif 'uc.cn' in url or 'drive.uc.cn' in url:
        return 'uc'
    # 123网盘
    elif '123' in url and ('123684.com' in url or '123865.com' in url or '123912.com' in url):
        return 'pan123'
    # 蓝奏云
    elif 'lanzou' in url or 'lanzoui.com' in url or 'lanzoux.com' in url:
        return 'lanzou'
    # 微云
    elif 'weiyun.com' in url:
        return 'weiyun'
    # 彩云
    elif 'caiyun.139.com' in url:
        return 'caiyun'
    # 115网盘
    elif '115.com' in url or '115cdn.com' in url:
        return '115'
    else:
        pass
        # 对于无法识别的链接，提取域名
        # try:
        #     parsed = urlparse(url)
        #     domain = parsed.netloc
        #     if domain:
        #         # 移除www前缀
        #         domain = re.sub(r'^www\.', '', domain)
        #         return f'unknown_{domain}'
        #     else:
        #         return 'unknown_invalid_url'
        # except:
        #     return 'unknown_parse_error'

# 按链接类型分类数据
cloud_type_data = defaultdict(list)

filtered_by_keyword = 0
processed_items = 0

if data.get('success') and data.get('data'):
    for channel in data['data']:
        if 'list' in channel:
            for item in channel['list']:
                title = item.get('title', '')
                cloud_links = item.get('cloudLinks', [])
                processed_items += 1

                # 检查标题是否包含搜索关键词（忽略大小写和HTML实体）
                import html
                clean_title = html.unescape(title).lower()  # 解码HTML实体并转小写
                clean_keyword = search_keyword.lower()

                if clean_keyword not in clean_title:
                    filtered_by_keyword += 1
                    # print(f"🚫 关键词过滤: {title[:50]}... (不包含'{search_keyword}')")
                    continue

                # 提取集数
                episode_number = extract_episode_number(title)

                for cloud_link in cloud_links:
                    link = cloud_link.get('link', '')
                    # 根据链接URL判断云盘类型
                    cloud_type = get_cloud_type_from_url(link)

                    cloud_type_data[cloud_type].append({
                        'title': title,
                        'link': link,
                        'channel': item.get('channel', ''),
                        'pubDate': item.get('pubDate', ''),
                        'episode': episode_number,
                        'video_count': 0  # 初始化视频数量为0
                    })


# 对所有网盘链接进行去重
print("\n🔄 对所有网盘链接进行去重...")
total_removed = 0

for cloud_type in cloud_type_data:
    if not cloud_type_data[cloud_type]:  # 跳过空列表
        continue

    original_count = len(cloud_type_data[cloud_type])

    # 使用字典来去重，key为链接，value为最新的数据项
    unique_links = {}

    for item in cloud_type_data[cloud_type]:
        link = item['link']
        if link not in unique_links:
            unique_links[link] = item
        else:
            # 如果链接已存在，保留发布时间最新的
            existing_item = unique_links[link]
            current_pubdate = item.get('pubDate', '')
            existing_pubdate = existing_item.get('pubDate', '')

            if current_pubdate > existing_pubdate:
                unique_links[link] = item

    # 更新数据
    cloud_type_data[cloud_type] = list(unique_links.values())
    deduplicated_count = len(cloud_type_data[cloud_type])
    removed_count = original_count - deduplicated_count
    total_removed += removed_count

    if removed_count > 0:
        print(f"  {cloud_type}: {original_count} -> {deduplicated_count} (去除了 {removed_count} 个重复链接)")

print(f"总共去除了 {total_removed} 个重复链接")

# 多线程处理夸克链接的视频文件数量
print("\n🔍 开始提取夸克链接的视频文件数量...")
quark_start_time = time.time()

quark_items = []
if 'quark' in cloud_type_data:
    quark_items = cloud_type_data['quark']
    print(f"找到 {len(quark_items)} 个夸克链接")

    if quark_items:
        print("正在多线程处理夸克链接...")
        # 使用线程池处理夸克链接
        with ThreadPoolExecutor(max_workers=5) as executor:
            # 提交所有任务
            future_to_item = {executor.submit(process_quark_link, item): item for item in quark_items}

            # 获取结果
            for future in as_completed(future_to_item):
                try:
                    result = future.result()
                    # 结果已经在process_quark_link中更新了
                except Exception as e:
                    print(f"❌ 处理夸克链接时出错: {str(e)}")

        quark_end_time = time.time()
        print(f"✅ 夸克链接视频文件数量提取完成，耗时: {quark_end_time - quark_start_time:.2f}秒")
else:
    print("未找到夸克链接")

# 检查是否为电影/综艺类型（全局判断）
def check_if_movie_or_variety(all_cloud_data):
    """检查所有数据是否为电影/综艺类型"""
    total_items = 0
    items_with_episode = 0

    for cloud_type, items in all_cloud_data.items():
        for item in items:
            total_items += 1
            if item.get('episode') is not None:
                items_with_episode += 1

    # 如果超过80%的项目没有集数信息，认为是电影/综艺
    if total_items > 0 and items_with_episode / total_items < 0.2:
        print(f"🎬 检测到电影/综艺类型 (有集数信息: {items_with_episode}/{total_items})")
        return True
    else:
        print(f"📺 检测到电视剧类型 (有集数信息: {items_with_episode}/{total_items})")
        return False

# 过滤和排序数据
def filter_and_sort_items(items, cloud_type_name, is_movie_or_variety):
    """过滤和排序数据项"""
    filtered_items = []
    filter_stats = {
        'total': len(items),
        'no_episode': 0,
        'no_video': 0,
        'episode_mismatch': 0,
        'passed': 0
    }

    for item in items:
        episode = item.get('episode')
        video_count = item.get('video_count', 0)
        title = item.get('title', '')[:60]

        # 如果是电影/综艺类型，允许没有集数信息的项目
        if is_movie_or_variety:
            # 对于电影/综艺，夸克链接要有视频文件，其他网盘直接保留
            if cloud_type_name == 'quark' and video_count == 0:
                filter_stats['no_video'] += 1
                print(f"🚫 过滤(无视频): {title}")
                continue
        else:
            # 对于电视剧，必须有集数信息
            if episode is None:
                filter_stats['no_episode'] += 1
                if cloud_type_name == 'quark':
                    print(f"🚫 过滤(无集数): {title}")
                continue

            # 只对夸克链接进行video_count过滤和差值过滤
            if cloud_type_name == 'quark':
                # 如果video_count等于0，不写入json
                if video_count == 0:
                    filter_stats['no_video'] += 1
                    print(f"🚫 过滤(无视频): {title}")
                    continue

                # 过滤条件：episode - video_count > 3 的不要
                if episode - video_count > 3:
                    filter_stats['episode_mismatch'] += 1
                    print(f"🚫 过滤(集数不匹配): {title} (声称:{episode}集, 实际:{video_count}个视频, 差值:{episode - video_count})")
                    continue

        filter_stats['passed'] += 1
        if cloud_type_name == 'quark':
            print(f"✅ 通过筛选: {title} (集数:{episode}, 视频:{video_count})")
        filtered_items.append(item)

    # 输出筛选统计
    if cloud_type_name == 'quark':
        print(f"\n📊 夸克链接筛选统计:")
        print(f"  总数: {filter_stats['total']}")
        print(f"  无集数信息: {filter_stats['no_episode']}")
        print(f"  无视频文件: {filter_stats['no_video']}")
        print(f"  集数不匹配: {filter_stats['episode_mismatch']}")
        print(f"  通过筛选: {filter_stats['passed']}")

    # 排序逻辑
    def sort_key(item):
        episode = item.get('episode')
        video_count = item.get('video_count', 0)

        if is_movie_or_variety:
            # 电影/综艺：夸克按视频文件数量降序，其他网盘按标题排序
            if cloud_type_name == 'quark':
                return (0, -video_count)
            else:
                return (1, item.get('title', ''))
        else:
            # 电视剧的排序逻辑
            if cloud_type_name == 'quark':
                # video_count >= episode 的排最前面
                if episode is not None and video_count >= episode:
                    return (0, -episode)  # 优先级最高，按集数降序
                else:
                    return (1, -episode if episode is not None else 0)  # 其次，按集数降序
            else:
                # 非夸克链接按集数降序排列
                return (2, -episode if episode is not None else 0)

    filtered_items.sort(key=sort_key)
    return filtered_items

# 全局检查是否为电影/综艺类型
is_movie_or_variety = check_if_movie_or_variety(cloud_type_data)

# 对每个云盘类型的数据进行过滤和排序
for cloud_type in cloud_type_data:
    cloud_type_data[cloud_type] = filter_and_sort_items(cloud_type_data[cloud_type], cloud_type, is_movie_or_variety)

# 按指定顺序排列：夸克最前面、然后百度、最后其他的
ordered_result = {}
priority_order = [
    'quark', 'baidu', 'aliyun', 'tianyi', 'xunlei', 'uc', 'pan123',
    '115', 'lanzou', 'ctfile', 'caiyun139', 'jianguoyun', 'weiyun',
    'onedrive', 'googledrive', 'dropbox'
]

for cloud_type in priority_order:
    if cloud_type in cloud_type_data:
        ordered_result[cloud_type] = cloud_type_data[cloud_type]

# 添加任何未在优先级列表中的类型
for cloud_type, items in cloud_type_data.items():
    if cloud_type not in ordered_result:
        ordered_result[cloud_type] = items

result = ordered_result

# 记录处理完成时间
process_time = time.time()
print(f"🔄 数据处理完成，耗时: {process_time - api_time:.2f}秒")

# 写入result.json文件
with open('result.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, ensure_ascii=False, indent=2)

# 记录结束时间
end_time = time.time()
total_time = end_time - start_time

print(f"\n✅ 数据已按cloudType分类并写入result.json文件")
print(f"📊 共找到 {len(result)} 种云盘类型:")
for cloud_type, items in result.items():
    print(f"  {cloud_type}: {len(items)} 个资源")

print(f"\n⏱️  时间统计:")
print(f"  API请求耗时: {api_time - start_time:.2f}秒")
print(f"  数据处理耗时: {process_time - api_time:.2f}秒")
print(f"  文件写入耗时: {end_time - process_time:.2f}秒")
print(f"  总耗时: {total_time:.2f}秒")
print(f"🏁 搜索完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")